import React, {
  useRef,
  useState,
  useImperativeHandle,
  forwardRef,
  Suspense,
  useEffect,
} from 'react';
import { Canvas } from '@react-three/fiber';
import {
  OrbitControls,
  PerspectiveCamera,
  Environment,
  Bounds,
} from '@react-three/drei';
import * as THREE from 'three';
import Model3<PERSON>enderer from './Model3DRenderer';
import {
  processResourceUrl,
  isBase64JSFile,
  loadBase64JSFileAndConvertToBlobUrl,
} from '../../shared/utils';

// Props for the R3FModel3DViewer component
export interface R3FModel3DViewerProps {
  modelUrl: string;
  modelFormat?: 'glb' | 'obj' | 'fbx';
  autoRotate?: boolean;
  rotationSpeed?: number;
  onLoad?: () => void;
  onError?: (error: any) => void;
  onProgress?: (progress: number) => void;
}

// Define methods that can be called from outside
export interface R3FModel3DViewerRef {
  resetView: () => void;
  rotateLeft: () => void;
  rotateRight: () => void;
  toggleAutoRotate: () => void;
  setRotationSpeed: (speed: number) => void;
}

// Main component
const R3FModel3DViewer: React.ForwardRefRenderFunction<
  R3FModel3DViewerRef,
  R3FModel3DViewerProps
> = (
  {
    modelUrl,
    modelFormat = 'glb',
    autoRotate = false,
    rotationSpeed = 1,
    onLoad,
    onError,
    onProgress,
  },
  ref
) => {
  const [isAutoRotating, setIsAutoRotating] = useState(autoRotate);
  const [currentRotationSpeed, setCurrentRotationSpeed] =
    useState(rotationSpeed);
  const [processedUrl, setProcessedUrl] = useState<string>('');
  const [isLoadingUrl, setIsLoadingUrl] = useState(false);

  const controlsRef = useRef<any>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera>(null);
  const blobUrlRef = useRef<string | null>(null);

  // Process the URL and handle base64 JS files
  useEffect(() => {
    const processUrl = async () => {
      if (!modelUrl) {
        setProcessedUrl('');
        return;
      }

      setIsLoadingUrl(true);

      try {
        // First, process the URL using our utility function
        const initialUrl = processResourceUrl(modelUrl, {
          addOrigin: true,
          encodeSpecialChars: true,
        });

        // Check if it's a base64 JS file
        if (isBase64JSFile(initialUrl)) {
          try {
            // Convert base64 JS file to blob URL
            const blobUrl = await loadBase64JSFileAndConvertToBlobUrl(
              initialUrl
            );
            blobUrlRef.current = blobUrl; // Store for cleanup
            setProcessedUrl(blobUrl);
            console.log('Successfully converted base64 JS to blob URL');
          } catch (conversionError) {
            console.warn(
              'Failed to convert base64 JS file, using original URL:',
              conversionError
            );
            // Fallback to original URL if conversion fails
            setProcessedUrl(initialUrl);
          }
        } else {
          // Use the processed URL directly
          setProcessedUrl(initialUrl);
        }
      } catch (error) {
        console.error('Error processing model URL:', error);
        // Fallback to original URL processing
        const fallbackUrl = processResourceUrl(modelUrl, {
          addOrigin: true,
          encodeSpecialChars: true,
        });
        setProcessedUrl(fallbackUrl);

        // Notify parent about the error
        if (onError) {
          onError(error);
        }
      } finally {
        setIsLoadingUrl(false);
      }
    };

    processUrl();

    // Cleanup function to revoke blob URLs
    return () => {
      if (blobUrlRef.current) {
        URL.revokeObjectURL(blobUrlRef.current);
        blobUrlRef.current = null;
      }
    };
  }, [modelUrl, onError]);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    resetView: () => {
      if (controlsRef.current) {
        controlsRef.current.reset();
      }
    },
    rotateLeft: () => {
      if (controlsRef.current) {
        controlsRef.current.rotateLeft(Math.PI / 4);
      }
    },
    rotateRight: () => {
      if (controlsRef.current) {
        controlsRef.current.rotateRight(Math.PI / 4);
      }
    },
    zoomIn: () => {
      if (controlsRef.current) {
        controlsRef.current.zoomIn(1.2);
      }
    },
    zoomOut: () => {
      if (controlsRef.current) {
        controlsRef.current.zoomOut(1.2);
      }
    },
    toggleAutoRotate: () => {
      setIsAutoRotating(!isAutoRotating);
    },
    setRotationSpeed: (speed: number) => {
      setCurrentRotationSpeed(speed);
    },
  }));

  return (
    <div className="r3f-model-3d-viewer tailwind-relative tailwind-w-full tailwind-h-full">
      <Canvas
        shadows
        gl={{ antialias: true }}
        style={{ background: '#f0f0f0' }}
      >
        <PerspectiveCamera
          makeDefault
          position={[0, 2, 5]}
          ref={cameraRef}
          fov={45}
          near={0.1}
          far={10000}
        />

        {/* Enhanced lighting setup for consistent model display */}
        <ambientLight intensity={0.6} />
        <directionalLight
          position={[5, 10, 7.5]}
          intensity={1}
          castShadow
          shadow-mapSize={[2048, 2048]}
          shadow-bias={-0.0001}
        >
          <orthographicCamera
            attach="shadow-camera"
            args={[-10, 10, 10, -10, 0.1, 50]}
          />
        </directionalLight>
        <directionalLight position={[-5, 5, -5]} intensity={0.5} />
        <directionalLight position={[0, -5, 0]} intensity={0.2} />

        {/* Hemisphere light for better ambient lighting */}
        <hemisphereLight
          args={[0xffffff, 0x8d8d8d, 0.3]}
          position={[0, 50, 0]}
        />

        {/* Environment for reflections and ambient lighting - simplified for cleaner display */}
        <Environment preset="city" background={false} />

        {/* Model with enhanced bounds for consistent auto-fitting */}
        <Bounds fit clip observe margin={1.5}>
          <Suspense fallback={null}>
            {/* Only render the model when URL is processed and not loading */}
            {processedUrl && !isLoadingUrl && (
              <Model3DRenderer
                url={processedUrl}
                format={modelFormat}
                autoRotate={isAutoRotating}
                rotationSpeed={currentRotationSpeed}
                onLoad={() => {
                  // Reset camera view after a short delay to ensure model is loaded
                  setTimeout(() => {
                    if (controlsRef.current) {
                      controlsRef.current.reset();
                    }
                  }, 200);

                  if (onLoad) onLoad();
                }}
                onError={(error: any) => {
                  if (onError) onError(error);
                }}
                onProgress={(progress: number) => {
                  if (onProgress) onProgress(progress);
                }}
              />
            )}
          </Suspense>
        </Bounds>

        {/* Enhanced controls for better user experience */}
        <OrbitControls
          ref={controlsRef}
          enableDamping
          dampingFactor={0.1}
          minDistance={0.1}
          maxPolarAngle={Math.PI * 0.95}
          minPolarAngle={0.05}
          enablePan={true}
          panSpeed={0.5}
          rotateSpeed={0.8}
          zoomSpeed={1.5}
          screenSpacePanning={true}
        />
      </Canvas>
    </div>
  );
};

export default forwardRef(R3FModel3DViewer);
