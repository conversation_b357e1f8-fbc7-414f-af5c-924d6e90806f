import React, { useRef, useEffect, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { Html, useBounds } from '@react-three/drei';
import { GLTFLoader } from 'three-stdlib';
import * as THREE from 'three';
// Import the integrated utility function
import { mightHaveCorsIssues } from '../../shared/utils';

// Error indicator component
export function ErrorIndicator({ error }: { error: string }) {
  return (
    <Html center>
      <div className="tailwind-text-center tailwind-p-4 tailwind-bg-red-50 tailwind-rounded-lg tailwind-shadow tailwind-max-w-md">
        <p className="tailwind-text-red-600 tailwind-font-medium">
          Không thể tải mô hình 3D
        </p>
        <p className="tailwind-text-gray-700 tailwind-text-sm tailwind-mt-1">
          {error}
        </p>
        <div className="tailwind-mt-3 tailwind-text-xs tailwind-text-gray-500 tailwind-text-left">
          <p className="tailwind-font-medium tailwind-mb-1">
            Kiểm tra các vấn đề sau:
          </p>
          <ul className="tailwind-list-disc tailwind-pl-4">
            <li>URL của mô hình có chính xác không?</li>
            <li>Định dạng tệp có được hỗ trợ không? (.glb, .obj, .fbx)</li>
            <li>Tệp có thể truy cập được không? (CORS, quyền truy cập)</li>
            <li>Tệp có bị hỏng hoặc không đúng định dạng không?</li>
            <li>
              Nếu là file base64 JS, kiểm tra quá trình convert có thành công
              không?
            </li>
          </ul>
        </div>
      </div>
    </Html>
  );
}

// Model component props
export interface Model3DRendererProps {
  url: string;
  format: 'glb' | 'obj' | 'fbx';
  autoRotate: boolean;
  rotationSpeed: number;
  onLoad?: () => void;
  onError?: (error: any) => void;
  onProgress?: (progress: number) => void;
}

// Helper function to create a standard material
const createStandardMaterial = (color = 0xcccccc, map?: THREE.Texture) => {
  return new THREE.MeshStandardMaterial({
    color,
    map,
    roughness: 0.7,
    metalness: 0.2,
    side: THREE.DoubleSide,
  });
};

// Helper function to process materials
const processMaterial = (material: THREE.Material | null) => {
  if (!material) {
    return createStandardMaterial();
  }

  material.needsUpdate = true;

  // Convert basic/phong materials to standard for better lighting
  if (
    material instanceof THREE.MeshBasicMaterial ||
    material instanceof THREE.MeshPhongMaterial
  ) {
    return createStandardMaterial(
      material.color.getHex(),
      material.map || undefined
    );
  }

  // Adjust standard material properties
  if (
    material instanceof THREE.MeshStandardMaterial ||
    material instanceof THREE.MeshPhysicalMaterial
  ) {
    if (material.roughness === 0) material.roughness = 0.5;
    if (material.metalness === 0) material.metalness = 0.2;
    material.side = THREE.DoubleSide;
  }

  return material;
};

// Model component
const Model3DRenderer: React.FC<Model3DRendererProps> = ({
  url,
  format,
  autoRotate,
  rotationSpeed,
  onLoad,
  onError,
  onProgress,
}) => {
  const groupRef = useRef<THREE.Group>(null);
  const bounds = useBounds();
  const [model, setModel] = useState<THREE.Object3D | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use refs to track current values of autoRotate and rotationSpeed
  // This allows us to access their current values in useFrame without dependencies
  const autoRotateRef = useRef(autoRotate);
  const rotationSpeedRef = useRef(rotationSpeed);

  // Update refs when props change
  useEffect(() => {
    autoRotateRef.current = autoRotate;
    rotationSpeedRef.current = rotationSpeed;
  }, [autoRotate, rotationSpeed]);

  // Handle model rotation using refs instead of direct props
  useFrame((_, delta) => {
    if (groupRef.current && autoRotateRef.current) {
      groupRef.current.rotation.y += delta * rotationSpeedRef.current * 0.5;
    }
  });

  // Helper function to process a loaded model
  const processLoadedModel = (model: THREE.Object3D) => {
    model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        // Process materials
        if (!child.material) {
          child.material = createStandardMaterial();
        } else if (Array.isArray(child.material)) {
          child.material = child.material.map(processMaterial);
        } else {
          child.material = processMaterial(child.material);
        }
        child.castShadow = true;
        child.receiveShadow = true;
      }
    });

    return model;
  };

  // Helper function to load a model with GLTFLoader (for all formats as requested)
  const loadModelWithGLTFLoader = async (
    loader: GLTFLoader,
    url: string
  ): Promise<THREE.Object3D> => {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('Model loading timeout after 20 seconds'));
      }, 20000);

      console.log('Loading model with GLTFLoader:', url);

      // Add a small delay to ensure progress starts from 0
      setTimeout(() => {
        loader.load(
          url,
          (gltf) => {
            clearTimeout(timeoutId);
            console.log('GLTF model loaded successfully:', gltf);
            resolve(gltf.scene); // GLTFLoader returns { scene }
          },
          (progressEvent) => {
            // Handle progress if available
            if (progressEvent.lengthComputable && onProgress) {
              const progress =
                (progressEvent.loaded / progressEvent.total) * 100;
              onProgress(Math.min(progress, 99)); // Cap at 99% until fully loaded
              console.log('Loading progress:', progress.toFixed(1) + '%');
            }
          },
          (error) => {
            clearTimeout(timeoutId);
            console.error('GLTF loading error:', error);
            reject(error);
          }
        );
      }, 100);
    });
  };

  // Load model based on format (using GLTFLoader for all formats as requested)
  useEffect(() => {
    if (!url) return;

    console.log('Starting model load process:', { url, format });

    // Reset state when loading a new model
    setLoading(true);
    setError(null);

    // Notify initial progress as 0%
    if (onProgress) {
      onProgress(0);
    }

    // Create a shared loading manager for the loader
    const manager = new THREE.LoadingManager();

    // Variable to track maximum progress achieved
    let maxProgress = 0;

    // Track loading progress
    manager.onProgress = (_, itemsLoaded, itemsTotal) => {
      // Calculate current progress
      const currentProgress = (itemsLoaded / itemsTotal) * 100;
      if (currentProgress > maxProgress) {
        maxProgress = currentProgress;
        if (onProgress) {
          onProgress(Math.min(maxProgress, 99)); // Cap at 99% until fully loaded
        }
      }
    };

    // Handle loading complete
    manager.onLoad = () => {
      setTimeout(() => {
        if (onProgress) {
          onProgress(100);
        }
      }, 100);
    };

    // Handle loading error
    manager.onError = (errorUrl) => {
      const errorMessage = `Failed to load file: ${errorUrl}`;
      console.error('Loading manager error:', errorMessage);
      setError(errorMessage);
      setLoading(false);
      if (onError) {
        onError(new Error(errorMessage));
      }
    };

    const loadModel = async () => {
      try {
        let loadedModel: THREE.Object3D | null = null;

        // Use GLTFLoader for all formats as requested
        // Note: This assumes all models are GLB/GLTF format or have been converted
        console.log('Using GLTFLoader for format:', format);
        const loader = new GLTFLoader(manager);
        loadedModel = await loadModelWithGLTFLoader(loader, url);

        if (loadedModel) {
          console.log('Model loaded, processing materials and shadows...');

          // Process the model (materials, shadows, etc.)
          processLoadedModel(loadedModel);

          // Set the model
          setModel(loadedModel);

          // Fit model to view
          if (groupRef.current) {
            // Wait for the next frame to ensure the model is in the scene
            setTimeout(() => {
              try {
                bounds.refresh().fit();
                console.log('Model fitted to bounds');
                setLoading(false);
                if (onLoad) onLoad();
              } catch (fitError) {
                console.warn('Error fitting model to bounds:', fitError);
                // Ensure loading state is updated even if fitting fails
                setLoading(false);
                if (onLoad) onLoad();
              }
            }, 100);
          } else {
            setLoading(false);
            if (onLoad) onLoad();
          }
        }
      } catch (error) {
        console.error('Model loading error:', error);

        // Provide more detailed error message
        let errorMessage = 'Could not load 3D model';

        if (error instanceof Error) {
          errorMessage = error.message;
        }

        // Check for network errors
        if (
          error instanceof TypeError &&
          error.message.includes('Failed to fetch')
        ) {
          errorMessage =
            'Network error: Could not access the model file. Check the URL and ensure it is accessible.';
        }

        // Check for CORS errors
        if (error instanceof DOMException && error.name === 'SecurityError') {
          errorMessage =
            'CORS error: The model file is blocked by cross-origin policy. Ensure proper CORS headers are set.';
        }

        // Proactively check for potential CORS issues using our integrated function
        if (mightHaveCorsIssues(url)) {
          errorMessage =
            'Possible CORS issue detected: The model is hosted on a different domain. ' +
            'This may cause loading problems. Consider hosting the model on the same server ' +
            'or configuring CORS headers on the server hosting the model.';
        }

        // Special handling for blob URLs (from base64 JS files)
        if (url.startsWith('blob:')) {
          errorMessage =
            'Error loading model from base64 data. The base64 conversion may have failed, ' +
            'or the original file format may not be compatible with GLTFLoader. ' +
            'Please ensure the original file is a valid GLB/GLTF model.';
        }

        // Handle invalid file format errors
        if (
          errorMessage.includes('Unexpected token') ||
          errorMessage.includes('JSON')
        ) {
          errorMessage =
            'Invalid file format: The file does not appear to be a valid GLB/GLTF model. ' +
            'Please ensure you are uploading a proper 3D model file.';
        }

        setError(errorMessage);
        setLoading(false);
        if (onError) onError(error);
      }
    };

    // Start the model loading process
    loadModel();

    // Add a safety timeout to ensure loading indicator doesn't get stuck
    const safetyTimeoutId = setTimeout(() => {
      if (loading) {
        console.warn('Safety timeout triggered, stopping loading indicator');
        setLoading(false);
        setError(
          'Loading timeout: The model took too long to load. Please try again or check the file.'
        );
      }
    }, 25000); // 25 seconds safety timeout

    // Clean up the safety timeout
    return () => clearTimeout(safetyTimeoutId);
  }, [url, bounds, onLoad, onError, onProgress]);

  // Don't show loading indicator here, let parent component handle it
  if (loading) {
    return null;
  }

  // If error, show error message
  if (error) {
    return <ErrorIndicator error={error} />;
  }

  // If no model, show empty state
  if (!model) {
    return <></>;
  }

  // Normalize and process the model
  const normalizeModel = (inputModel: THREE.Object3D): THREE.Object3D => {
    // Clone the model to avoid modifying the original
    const processedModel = inputModel.clone();

    // Normalize the model scale and position
    const box = new THREE.Box3().setFromObject(processedModel);
    const size = box.getSize(new THREE.Vector3());
    const center = box.getCenter(new THREE.Vector3());

    // Calculate scale to normalize size (max dimension to 2 units)
    const maxDimension = Math.max(size.x, size.y, size.z);
    const scale = maxDimension > 0 ? 2 / maxDimension : 1;

    // Apply transformations to the model
    processedModel.scale.multiplyScalar(scale);
    processedModel.position.sub(center.multiplyScalar(scale));

    console.log('Model normalized:', {
      originalSize: size,
      maxDimension,
      scale,
      center,
    });

    return processedModel;
  };

  // Process the model for consistent display
  const processedModel = normalizeModel(model);

  // Return the processed model
  return (
    <group ref={groupRef}>
      <primitive object={processedModel} />
    </group>
  );
};

export default Model3DRenderer;
